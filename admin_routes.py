#!/usr/bin/env python3
"""
Route API per il sistema di amministrazione SNIP
Endpoint per gestione utenti, configurazioni, audit e statistiche
"""

from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, Form
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from pydantic import BaseModel

from database import get_db
from models import Agente, RuoloEnum, RepartoEnum, SystemConfig, AuditLog, UserSession, SystemStats
from session_manager import require_min_role, get_current_user
from admin_manager import admin_manager
import logging

# Setup logging
logger = logging.getLogger(__name__)

# Router per le API admin
admin_router = APIRouter(prefix="/admin", tags=["Amministrazione"])

# ===== MODELLI PYDANTIC =====

class UserCreate(BaseModel):
    nome: str
    cognome: str
    email: str
    password: str
    reparto: str
    ruolo: str = "USER"
    visibile: str = "no"

class UserUpdate(BaseModel):
    nome: Optional[str] = None
    cognome: Optional[str] = None
    email: Optional[str] = None
    password: Optional[str] = None
    reparto: Optional[str] = None
    ruolo: Optional[str] = None
    visibile: Optional[str] = None

class ConfigCreate(BaseModel):
    config_key: str
    config_value: str
    description: Optional[str] = None
    config_type: str = "string"

class UserResponse(BaseModel):
    id_user: int
    Nome: str
    Cognome: str
    email: str
    reparto: str
    ruolo: str
    visibile: str

    class Config:
        from_attributes = True

# ===== ENDPOINT GESTIONE UTENTI =====

@admin_router.get("/users", response_model=List[UserResponse])
def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene lista utenti con paginazione e ricerca"""
    try:
        if search:
            users = admin_manager.search_users(db, search, skip, limit)
        else:
            users = admin_manager.get_all_users(db, skip, limit)
        
        return users
        
    except Exception as e:
        logger.error(f"Errore get users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/users/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene un utente specifico"""
    user = admin_manager.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="Utente non trovato")
    return user

@admin_router.post("/users", response_model=UserResponse)
def create_user(
    user_data: UserCreate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Crea un nuovo utente"""
    try:
        # Type hint per il type checker
        user_id: int = current_user.id_user  # type: ignore
        new_user = admin_manager.create_user(
            db=db,
            user_data=user_data.dict(),
            created_by=user_id
        )
        return new_user
        
    except Exception as e:
        logger.error(f"Errore create user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.put("/users/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Aggiorna un utente esistente"""
    try:
        # Filtra solo i campi non None
        update_data = {k: v for k, v in user_data.dict().items() if v is not None}

        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        updated_user = admin_manager.update_user(
            db=db,
            user_id=user_id,
            user_data=update_data,
            updated_by=user_id_int
        )
        return updated_user
        
    except Exception as e:
        logger.error(f"Errore update user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.delete("/users/{user_id}")
def delete_user(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Elimina un utente (soft delete)"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        success = admin_manager.delete_user(
            db=db,
            user_id=user_id,
            deleted_by=user_id_int
        )
        
        if success:
            return {"message": "Utente eliminato con successo"}
        else:
            raise HTTPException(status_code=404, detail="Utente non trovato")
            
    except Exception as e:
        logger.error(f"Errore delete user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT CONFIGURAZIONI =====

@admin_router.get("/api/configurations")
def get_all_configurations(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le configurazioni organizzate per sezioni"""
    try:
        # Ottieni tutte le configurazioni
        configs = admin_manager.get_all_configs(db)

        # Organizza per sezioni
        organized_configs = {
            "database": {},
            "security": {},
            "sof": {},
            "email": {}
        }

        # Mappa le configurazioni alle sezioni appropriate
        for config in configs:
            key = str(config.config_key or "")
            value = str(config.config_value or "")

            # Configurazioni database/backup
            if key == 'backup_time':
                organized_configs["database"]["backup_time"] = value
            elif key == 'backup_retention':
                organized_configs["database"]["backup_retention"] = int(value) if value and value.isdigit() else 30
            elif key == 'backup_frequency':
                organized_configs["database"]["backup_schedule"] = value
            elif key == 'backup_compress':
                organized_configs["database"]["compress_backup"] = value.lower() in ['true', '1', 'yes'] if value else True
            # Configurazioni email
            elif key == 'email_admin_email':
                organized_configs["email"]["admin_email"] = value
            elif key == 'email_sender_email':
                organized_configs["email"]["sender_email"] = value
            # Altri mapping possono essere aggiunti qui

        # Valori predefiniti per database
        organized_configs["database"].setdefault("backup_time", "02:00")
        organized_configs["database"].setdefault("backup_retention", 30)
        organized_configs["database"].setdefault("backup_schedule", "daily")
        organized_configs["database"].setdefault("compress_backup", True)
        organized_configs["database"].setdefault("backup_path", "/backups/snip/")

        return {
            "success": True,
            "configurations": organized_configs
        }

    except Exception as e:
        logger.error(f"Errore get configurations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/api/configurations")
def save_all_configurations(
    configurations: Dict[str, Any],
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Salva tutte le configurazioni dal form admin dashboard"""
    try:
        user_id_int: int = current_user.id_user  # type: ignore
        saved_configs = []

        # Salva configurazioni database/backup
        if "database" in configurations:
            db_config = configurations["database"]

            # Mappa le configurazioni del form alle chiavi del backup
            config_mappings = [
                ("backup_time", db_config.get("backup_time", "02:00")),
                ("backup_retention", str(db_config.get("backup_retention", 30))),
                ("backup_frequency", db_config.get("backup_schedule", "daily")),
                ("backup_compress", str(db_config.get("compress_backup", True)).lower())
            ]

            for key, value in config_mappings:
                config = admin_manager.set_config(
                    db=db,
                    key=key,
                    value=value,
                    description=f"Configurazione backup: {key}",
                    config_type="string",
                    user_id=user_id_int
                )
                saved_configs.append(f"{key}={value}")

        # Salva configurazioni email se presenti
        if "email" in configurations:
            email_config = configurations["email"]

            email_mappings = [
                ("email_admin_email", email_config.get("admin_email", "")),
                ("email_sender_email", email_config.get("sender_email", ""))
            ]

            for key, value in email_mappings:
                if value:  # Solo se il valore non è vuoto
                    config = admin_manager.set_config(
                        db=db,
                        key=key,
                        value=value,
                        description=f"Configurazione email: {key}",
                        config_type="string",
                        user_id=user_id_int
                    )
                    saved_configs.append(f"{key}={value}")

        logger.info(f"Configurazioni salvate da {current_user.email}: {saved_configs}")

        return {
            "success": True,
            "message": f"Configurazioni salvate con successo: {len(saved_configs)} elementi",
            "saved_configs": saved_configs
        }

    except Exception as e:
        logger.error(f"Errore save configurations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/config")
def get_all_configs(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le configurazioni di sistema"""
    try:
        configs = admin_manager.get_all_configs(db)
        return [
            {
                "id": config.id,
                "config_key": config.config_key,
                "config_value": config.config_value,
                "description": config.description,
                "config_type": config.config_type,
                "is_active": config.is_active,
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat()
            }
            for config in configs
        ]

    except Exception as e:
        logger.error(f"Errore get configs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/config/{key}")
def get_config(
    key: str,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene una configurazione specifica"""
    config = admin_manager.get_config(db, key)
    if not config:
        raise HTTPException(status_code=404, detail="Configurazione non trovata")
    
    return {
        "id": config.id,
        "config_key": config.config_key,
        "config_value": config.config_value,
        "description": config.description,
        "config_type": config.config_type,
        "is_active": config.is_active,
        "created_at": config.created_at.isoformat(),
        "updated_at": config.updated_at.isoformat()
    }

@admin_router.post("/config")
def set_config(
    config_data: ConfigCreate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Imposta o aggiorna una configurazione"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        config = admin_manager.set_config(
            db=db,
            key=config_data.config_key,
            value=config_data.config_value,
            description=config_data.description,
            config_type=config_data.config_type,
            user_id=user_id_int
        )
        
        return {
            "id": config.id,
            "config_key": config.config_key,
            "config_value": config.config_value,
            "description": config.description,
            "config_type": config.config_type,
            "message": "Configurazione salvata con successo"
        }
        
    except Exception as e:
        logger.error(f"Errore set config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT AUDIT LOG =====

@admin_router.get("/audit")
def get_audit_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user_id: Optional[int] = Query(None),
    action: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene i log di audit con filtri"""
    try:
        logs = admin_manager.get_audit_logs(
            db=db,
            skip=skip,
            limit=limit,
            user_id=user_id,
            action=action,
            start_date=start_date,
            end_date=end_date
        )
        
        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "user_email": log.user.email if log.user else None,
                "action": log.action,
                "table_name": log.table_name,
                "record_id": log.record_id,
                "old_values": log.old_values,
                "new_values": log.new_values,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "timestamp": log.timestamp.isoformat()
            }
            for log in logs
        ]
        
    except Exception as e:
        logger.error(f"Errore get audit logs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT STATISTICHE =====

@admin_router.get("/stats/dashboard")
def get_dashboard_stats(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene statistiche per la dashboard admin"""
    try:
        return admin_manager.get_dashboard_summary(db)
        
    except Exception as e:
        logger.error(f"Errore dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/stats/daily")
def get_daily_stats(
    stat_date: Optional[date] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene statistiche giornaliere"""
    try:
        if not stat_date:
            stat_date = date.today()
        
        stats = admin_manager.get_system_stats(db, stat_date)
        if not stats:
            # Genera statistiche se non esistono
            stats = admin_manager.update_daily_stats(db, stat_date)
        
        return {
            "stat_date": stats.stat_date.isoformat(),
            "total_users": stats.total_users,
            "active_users": stats.active_users,
            "total_viaggi": stats.total_viaggi,
            "sof_generated": stats.sof_generated,
            "login_count": stats.login_count,
            "error_count": stats.error_count,
            "created_at": stats.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Errore daily stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/stats/update")
def update_stats(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Aggiorna le statistiche giornaliere"""
    try:
        stats = admin_manager.update_daily_stats(db)
        
        return {
            "message": "Statistiche aggiornate con successo",
            "stat_date": stats.stat_date.isoformat(),
            "total_users": stats.total_users,
            "active_users": stats.active_users,
            "total_viaggi": stats.total_viaggi
        }
        
    except Exception as e:
        logger.error(f"Errore update stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT GESTIONE SESSIONI =====

@admin_router.get("/sessions")
def get_active_sessions(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le sessioni attive"""
    try:
        sessions = admin_manager.get_active_sessions(db)

        return [
            {
                "id": session.id,
                "user_id": session.user_id,
                "user_email": session.user.email if session.user else None,
                "user_name": f"{session.user.Nome} {session.user.Cognome}" if session.user else None,
                "session_token": getattr(session, 'session_token', '')[:20] + "..." if getattr(session, 'session_token', None) else None,
                "ip_address": session.ip_address,
                "user_agent": session.user_agent,
                "is_active": session.is_active,
                "last_activity": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'last_activity', None)),
                "created_at": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'created_at', None)),
                "expires_at": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'expires_at', None))
            }
            for session in sessions
        ]

    except Exception as e:
        logger.error(f"Errore get sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/sessions/{session_id}/invalidate")
def invalidate_session(
    session_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Invalida una sessione specifica"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        success = admin_manager.invalidate_session(db, session_id, user_id_int)

        if success:
            return {"message": f"Sessione {session_id} invalidata con successo"}
        else:
            raise HTTPException(status_code=404, detail="Sessione non trovata")

    except Exception as e:
        logger.error(f"Errore invalidate session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/users/{user_id}/invalidate-sessions")
def invalidate_user_sessions(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Invalida tutte le sessioni di un utente"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        count = admin_manager.invalidate_user_sessions(db, user_id, user_id_int)

        return {
            "message": f"{count} sessioni invalidate per utente {user_id}",
            "sessions_invalidated": count
        }

    except Exception as e:
        logger.error(f"Errore invalidate user sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT UTILITÀ =====

@admin_router.get("/cleanup")
def cleanup_page(
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN))
):
    """Pagina cleanup sistema"""
    return {
        "message": "Pagina cleanup sistema",
        "description": "Utilizza POST /admin/cleanup?days_to_keep=90 per eseguire la pulizia",
        "available_actions": [
            "Pulizia log audit vecchi",
            "Rimozione sessioni scadute",
            "Cleanup dati temporanei"
        ]
    }

@admin_router.post("/cleanup")
def cleanup_old_data(
    days_to_keep: int = Query(90, ge=1, le=365),
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Pulisce dati vecchi dal sistema"""
    try:
        result = admin_manager.cleanup_old_data(db, days_to_keep)

        return {
            "message": "Cleanup completato con successo",
            "days_kept": days_to_keep,
            "results": result
        }

    except Exception as e:
        logger.error(f"Errore cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/system/health")
def system_health(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Controlla lo stato di salute del sistema"""
    try:
        # Test connessione database
        db.execute(text("SELECT 1"))
        db_status = "OK"

        # Conta sessioni attive
        active_sessions = len(admin_manager.get_active_sessions(db))

        # Statistiche base
        summary = admin_manager.get_dashboard_summary(db)

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "active_sessions": active_sessions,
            "system_info": {
                "total_users": summary["totals"]["users"],
                "total_viaggi": summary["totals"]["viaggi"],
                "recent_activity": summary["activity"]["recent_actions"]
            }
        }

    except Exception as e:
        logger.error(f"Errore system health: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT BACKUP =====

@admin_router.post("/backup/manual")
def create_manual_backup(
    format_type: str = Query("custom", regex="^(sql|custom)$"),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Crea backup manuale del database"""
    try:
        from backup_manager import BackupManager
        from config import settings

        # Inizializza backup manager
        db_url = settings.DATABASE_URL
        backup_manager = BackupManager(db_url)

        logger.info(f"[BACKUP MANUALE] Richiesto da utente {current_user.Nome} {current_user.Cognome} (ID: {current_user.id_user})")

        # Crea backup
        backup_path = backup_manager.create_backup(format_type=format_type)

        if backup_path:
            # Log audit
            user_id_int: int = current_user.id_user  # type: ignore
            admin_manager.log_action(
                db=db,
                user_id=user_id_int,
                action="BACKUP_MANUAL",
                table_name="SYSTEM",
                new_values=f"Backup manuale creato: {backup_path}",
                ip_address="127.0.0.1"
            )

            from pathlib import Path
            backup_file = Path(backup_path)
            file_size = backup_file.stat().st_size if backup_file.exists() else 0

            return {
                "success": True,
                "message": "Backup creato con successo",
                "backup_path": str(backup_path),
                "backup_file": backup_file.name,
                "file_size": file_size,
                "format": format_type,
                "created_by": f"{current_user.Nome} {current_user.Cognome}",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Errore durante la creazione del backup")

    except Exception as e:
        logger.error(f"Errore backup manuale: {str(e)}")

        # Log errore
        try:
            user_id_int: int = current_user.id_user  # type: ignore
            admin_manager.log_action(
                db=db,
                user_id=user_id_int,
                action="BACKUP_MANUAL_ERROR",
                table_name="SYSTEM",
                new_values=f"Errore backup manuale: {str(e)}",
                ip_address="127.0.0.1"
            )
        except:
            pass

        raise HTTPException(status_code=500, detail=f"Errore durante backup: {str(e)}")

@admin_router.get("/backup/status")
def get_backup_status(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene stato del sistema di backup"""
    try:
        from backup_manager import BackupManager
        from config import settings
        from pathlib import Path

        # Inizializza backup manager
        db_url = settings.DATABASE_URL
        backup_manager = BackupManager(db_url)

        # Ottieni configurazioni backup
        config = backup_manager.get_backup_config()

        # Lista backup esistenti
        backup_dir = Path("backups")
        backup_files = []

        if backup_dir.exists():
            for backup_file in backup_dir.glob("snip_backup_*"):
                file_stat = backup_file.stat()
                backup_files.append({
                    "name": backup_file.name,
                    "size": file_stat.st_size,
                    "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "format": "custom" if ".dump" in backup_file.name else "sql"
                })

        # Ordina per data (più recenti prima)
        backup_files.sort(key=lambda x: x["created"], reverse=True)

        return {
            "backup_config": config,
            "recent_backups": backup_files[:10],  # Ultimi 10 backup
            "backup_directory": str(backup_dir.absolute()),
            "total_backups": len(backup_files)
        }

    except Exception as e:
        logger.error(f"Errore backup status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/system/info")
def system_info(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN))
):
    """Ottiene informazioni di sistema"""
    import platform
    import sys

    return {
        "system": {
            "platform": platform.platform(),
            "python_version": sys.version,
            "architecture": platform.architecture()[0]
        },
        "application": {
            "name": "SNIP - Sistema Navale Integrato Portuale",
            "version": "2.0.0",
            "environment": "development"  # TODO: da configurazione
        },
        "timestamp": datetime.now().isoformat()
    }
