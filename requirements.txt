# ===== SNIP - Sistema Navale Integrato Portuale =====
# Requirements.txt completo per l'applicazione SNIP
# Generato il 2025-06-17

# ===== FRAMEWORK WEB =====
fastapi==0.104.1
uvicorn[standard]==0.24.0

# ===== DATABASE =====
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# ===== AUTENTICAZIONE E SICUREZZA =====
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# ===== TEMPLATING E FRONTEND =====
jinja2==3.1.2
aiofiles==23.2.1

# ===== GENERAZIONE DOCUMENTI =====
python-docx==1.1.2
lxml==5.4.0
reportlab==4.0.7

# ===== GESTIONE DATE E TEMPO =====
python-dateutil==2.8.2
schedule==1.2.0

# ===== VALIDAZIONE DATI =====
pydantic==2.5.0
pydantic-settings==2.1.0

# ===== LOGGING E MONITORING =====
structlog==23.2.0

# ===== UTILITY =====
typing-extensions==4.14.0
email-validator==2.1.0

# ===== SVILUPPO E DEBUG (opzionali) =====
# Decommentare se necessario per sviluppo
# pytest==7.4.3
# pytest-asyncio==0.21.1
# black==23.11.0
# flake8==6.1.0
# mypy==1.7.1

# ===== DIPENDENZE SISTEMA =====
# Per Windows: Microsoft Visual C++ 14.0 o superiore
# Per Linux: python3-dev, libpq-dev, build-essential

# ===== NOTE INSTALLAZIONE =====
# 1. Creare virtual environment:
#    python -m venv venv
#    
# 2. Attivare virtual environment:
#    Windows: venv\Scripts\activate
#    Linux/Mac: source venv/bin/activate
#    
# 3. Installare dipendenze:
#    pip install -r requirements.txt
#    
# 4. Configurare database PostgreSQL:
#    - Creare database 'AGENTE'
#    - Configurare credenziali in config.py
#    
# 5. Avviare applicazione:
#    uvicorn main:app --reload --host 0.0.0.0 --port 8002

# ===== VERSIONI PYTHON SUPPORTATE =====
# Python >= 3.8
# Testato con Python 3.11, 3.12, 3.13

# ===== DIPENDENZE PER FILE EXCEL E ANALISI DATI =====
openpyxl==3.1.2
pandas==2.1.4
xlrd>=2.0.1

# ===== DIPENDENZE OPZIONALI PER FUNZIONALITÀ AVANZATE =====
# Per notifiche email (se implementate):
# sendgrid==6.10.0
#
# Per cache Redis (se implementato):
# redis==5.0.1
#
# Per grafici e report avanzati:
# matplotlib==3.8.2
# plotly==5.17.0
